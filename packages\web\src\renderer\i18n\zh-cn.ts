/*
|--------------------------------------------------------------------------
| 简体中文语言包
|--------------------------------------------------------------------------
*/
export default {
  基于Vue和Electron的接口文档工具: '基于Vue和Electron的接口文档工具',
  项目列表: '项目列表',
  GitHub地址: 'GitHub地址',
  Gitee地址: 'Gitee地址',
  最近一次更新: '最近一次更新',
  api文档: 'api文档',
  权限管理: '权限管理',
  刷新: '刷新',
  前进: '前进',
  后退: '后退',
  撤销: '撤销',
  重做: '重做',
  撤销了: '撤销了',
  重做了: '重做了',
  修改协议: '修改协议',
  修改请求路径: '修改请求路径',
  修改请求前缀: '修改请求前缀',
  添加请求头: '添加请求头',
  修改请求头: '修改请求头',
  删除请求头: '删除请求头',
  调整请求头顺序: '调整请求头顺序',
  添加查询参数: '添加查询参数',
  修改查询参数: '修改查询参数',
  删除查询参数: '删除查询参数',
  调整查询参数顺序: '调整查询参数顺序',
  修改发送消息: '修改发送消息',
  修改连接配置: '修改连接配置',
  修改前置脚本: '修改前置脚本',
  修改后置脚本: '修改后置脚本',
  修改接口名称: '修改接口名称',
  修改接口描述: '修改接口描述',
  修改基本信息: '修改基本信息',
  消息内容: '消息内容',
  查询参数: '查询参数',
  连接配置: '连接配置',
  前置脚本: '前置脚本',
  后置脚本: '后置脚本',
  备注信息: '备注信息',
  连接信息: '连接信息',
  切换语言: '切换语言',
  更新进度: '更新进度',
  安装: '安装',
  个人中心: '个人中心',
  检查更新: '检查更新',
  版本: '版本',
  退出登录: '退出登录',
  存在可用更新: '存在可用更新',
  没有可用更新: '没有可用更新',
  暂无可用更新: '暂无可用更新',
  下载中: '下载中',
  下载完成: '下载完成',
  更新异常请稍后再试: '更新异常请稍后再试',
  账号登录: '账号登录',
  手机登录: '手机登录',
  账号注册: '账号注册',
  忘记密码: '忘记密码',
  请输入用户名: '请输入用户名',
  请输入密码: '请输入密码',
  '直接登录(体验账号，数据不会被保存)': '直接登录(体验账号，数据不会被保存)',
  登录: '登录',
  注册账号: '注册账号',
  '已有账号，忘记密码?': '已有账号，忘记密码?',
  跳转github: '跳转github',
  跳转码云: '跳转码云',
  跳转文档: '跳转文档',
  完整文档: '完整文档',
  跳转部署文档: '跳转部署文档',
  部署文档: '部署文档',
  客户端下载: '客户端下载',
  验证码: '验证码',
  请输入手机号: '请输入手机号',
  请输入验证码: '请输入验证码',
  请填写正确手机号: '请填写正确手机号',
  请输入登录名称: '请输入登录名称',
  请再次输入密码: '请再次输入密码',
  注册并登录: '注册并登录',
  请输入重置后密码: '请输入重置后密码',
  '只允许 数字  字符串 ! @ # 不允许其他字符串': '只允许 数字  字符串 ! @ # 不允许其他字符串',
  '数字+字符串，并且大于8位': '数字+字符串，并且大于8位',
  请完善必填信息: '请完善必填信息',
  '两次输入密码不一致!': '两次输入密码不一致!',
  重置密码成功: '重置密码成功',
  确定批量删除: '确定批量删除',
  '个节点?': '个节点?',
  节点: '节点',
  提示: '提示',
  确定: '确定',
  取消: '取消',
  确定删除: '确定删除',
  新增文件夹: '新增文件夹',
  新增文件: '新增文件',
  刷新banner: '刷新banner',
  项目分享: '项目分享',
  回收站: '回收站',
  导出文档: '导出文档',
  导入文档: '导入文档',
  操作审计: '操作审计',
  全局设置: '全局设置',
  '文档名称、文档url': '文档名称、文档url',
  高级筛选: '高级筛选',
  操作人员: '操作人员',
  清空: '清空',
  录入日期: '录入日期',
  今天: '今天',
  近两天: '近两天',
  近三天: '近三天',
  近七天: '近七天',
  自定义: '自定义',
  至: '至',
  开始日期: '开始日期',
  结束日期: '结束日期',
  最近多少条: '最近多少条',
  '2条': '2条',
  '5条': '5条',
  '10条': '10条',
  '15条': '15条',
  更多操作: '更多操作',
  快捷操作: '快捷操作',
  点击发起连接建立WebSocket连接: '点击发起连接建立WebSocket连接',
  不能为空: '不能为空',
  新建文档: '新建文档',
  新建文件夹: '新建文件夹',
  剪切: '剪切',
  复制: '复制',
  生成副本: '生成副本',
  粘贴: '粘贴',
  重命名: '重命名',
  删除: '删除',
  批量剪切: '批量剪切',
  批量复制: '批量复制',
  批量删除: '批量删除',
  以模板新建: '以模板新建',
  单个文件夹里面文档个数不超过: '单个文件夹里面文档个数不超过',
  全局设置中可配置: '全局设置中可配置',
  个: '个',
  '域名、接口前缀、环境维护': '域名、接口前缀、环境维护',
  符合规范的接口前缀: '符合规范的接口前缀',
  'ip地址+路径(可选)': 'ip地址+路径(可选)',
  例如: '例如',
  '域名+路径(可选)': '域名+路径(可选)',
  前缀名称: '前缀名称',
  '例如：张三本地': '例如：张三本地',
  'ip+端口或域名': 'ip+端口或域名',
  请选择协议: '请选择协议',
  没有则不填: '没有则不填',
  不填则默认80: '不填则默认80',
  端口: '端口',
  ip或域名: 'ip或域名',
  协议: '协议',
  接口前缀: '接口前缀',
  是否共享: '是否共享',
  仅自身可见: '仅自身可见',
  项目内成员可见: '项目内成员可见',
  确认添加: '确认添加',
  接口前缀必填: '接口前缀必填',
  操作: '操作',
  编辑: '编辑',
  确认: '确认',
  关闭: '关闭',
  接口前缀不符合规范: '接口前缀不符合规范',
  请输入前缀名称: '请输入前缀名称',
  限制可维护域名数不超过: '限制可维护域名数不超过',
  修改成功: '修改成功',
  '此操作将永久删除此条记录, 是否继续?': '此操作将永久删除此条记录, 是否继续?',
  接口前缀不能为空: '接口前缀不能为空',
  '当前请求方法被禁止，可以在全局配置中进行相关配置': '当前请求方法被禁止，可以在全局配置中进行相关配置',
  环境维护: '环境维护',
  代理: '代理',
  '路径参数写法': '路径参数写法',
  '由于浏览器限制，非electron环境无法模拟发送请求': '由于浏览器限制，非electron环境无法模拟发送请求',
  发送请求: '发送请求',
  取消请求: '取消请求',
  保存接口: '保存接口',
  导入参数: '导入参数',
  格式化JSON: '格式化JSON',
  确认导入: '确认导入',
  无法解析该字符串: '无法解析该字符串',
  保存参数为模板: '保存参数为模板',
  模板名称: '模板名称',
  保存: '保存',
  应用模板: '应用模板',
  过滤模板: '过滤模板',
  维护: '维护',
  暂无数据: '暂无数据',
  保存为模板: '保存为模板',
  'raw模块中json数据可用于快速调试，参数无法添加备注，如果需要添加备注可以选择在json模块中录入参数': 'raw模块中json数据可用于快速调试，参数无法添加备注，如果需要添加备注可以选择在json模块中录入参数',
  点击隐藏: '点击隐藏',
  个隐藏: '个隐藏',
  原始值: '原始值',
  参数: '参数',
  名称: '名称',
  修改名称: '修改名称',
  状态码: '状态码',
  返回格式: '返回格式',
  新增: '新增',
  请求参数: '请求参数',
  Query参数: 'Query参数',
  Path参数: 'Path参数',
  'Body参数(application/json)': 'Body参数(application/json)',
  'Body参数(multipart/formdata)': 'Body参数(multipart/formdata)',
  'Body参数(x-www-form-urlencoded)': 'Body参数(x-www-form-urlencoded)',
  Body参数: 'Body参数',
  返回参数: '返回参数',
  请求头: '请求头',
  预览: '预览',
  布局: '布局',
  左右布局: '左右布局',
  上下布局: '上下布局',
  变量: '变量',
  联想值: '联想值',
  未实现的返回类型: '未实现的返回类型',
  变量维护: '变量维护',
  联想参数: '联想参数',
  基本信息: '基本信息',
  请求地址: '请求地址',
  请求方式: '请求方式',
  维护人员: '维护人员',
  创建人员: '创建人员',
  累计用时: '累计用时',
  更新日期: '更新日期',
  创建日期: '创建日期',
  下载文件: '下载文件',
  应用为响应值: '应用为响应值',
  应用为: '应用为',
  未命名: '未命名',
  总大小: '总大小',
  已传输: '已传输',
  进度: '进度',
  值: '值',
  时长: '时长',
  未请求数据: '未请求数据',
  大小: '大小',
  格式: '格式',
  返回值: '返回值',
  返回头: '返回头',
  点击发送请求按钮发送请求: '点击发送请求按钮发送请求',
  '因浏览器限制，完整HTTP功能请下载Electron': '因浏览器限制，完整HTTP功能请下载Electron',
  '跨域、、请求头(user-agent,accept-encoding)等受限': '跨域、、请求头(user-agent,accept-encoding)等受限',
  下载Electron: '下载Electron',
  导出类型: '导出类型',
  JSON文档: 'JSON文档',
  导出到其他项目: '导出到其他项目',
  额外配置: '额外配置',
  选择导出: '选择导出',
  开启后可以自由选择需要导出的文档: '开启后可以自由选择需要导出的文档',
  总数: '总数',
  文件夹数量: '文件夹数量',
  文档数量: '文档数量',
  确定导出: '确定导出',
  请至少选择一个文档导出: '请至少选择一个文档导出',
  将当前项目指定文档导出到其他项目: '将当前项目指定文档导出到其他项目',
  '从左侧拖拽文档到右侧，右侧也可以进行简单的拖拽': '从左侧拖拽文档到右侧，右侧也可以进行简单的拖拽',
  鼠标右键可以新增文件夹或者删除文件夹: '鼠标右键可以新增文件夹或者删除文件夹',
  '暂无文档，请在项目中添加至少一个文档': '暂无文档，请在项目中添加至少一个文档',
  导入成功: '导入成功',
  当前版本: '当前版本',
  今日新增: '今日新增',
  接口总数: '接口总数',
  '支持：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1': '支持：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1',
  '将文件拖到此处，或': '将文件拖到此处，或',
  点击上传: '点击上传',
  文档类型: '文档类型',
  导入数据预览: '导入数据预览',
  文档数: '文档数',
  文件夹数: '文件夹数',
  文件夹命名方式: '文件夹命名方式',
  'none代表不存在文件夹，所有节点扁平放置': 'none代表不存在文件夹，所有节点扁平放置',
  导入方式: '导入方式',
  请谨慎选择导入方式: '请谨慎选择导入方式',
  追加方式: '追加方式',
  覆盖方式: '覆盖方式',
  目标目录: '目标目录',
  '选择需要挂载的节点，不选择则默认挂载到根目录': '选择需要挂载的节点，不选择则默认挂载到根目录',
  确定导入: '确定导入',
  '未知的文件格式，无法解析': '未知的文件格式，无法解析',
  仅支持JSON格式或者YAML格式文件: '仅支持JSON格式或者YAML格式文件',
  文件大小不超过: '文件大小不超过',
  覆盖后的数据将无法还原: '覆盖后的数据将无法还原',
  请选择需要导入的文件: '请选择需要导入的文件',
  缺少Version信息: '缺少Version信息',
  缺少Info字段: '缺少Info字段',
  缺少servers字段: '缺少servers字段',
  servers字段必须为数组: 'servers字段必须为数组',
  'server对象中存在多个变量枚举值，但接口工具仅解析默认值': 'server对象中存在多个变量枚举值，但接口工具仅解析默认值',
  服务器: '服务器',
  缺少paths字段: '缺少paths字段',
  路径: '路径',
  相关属性为空: '相关属性为空',
  paths参数中存在方法: 'paths参数中存在方法',
  但是所匹配数据为空: '但是所匹配数据为空',
  链接名称: '链接名称',
  生成链接: '生成链接',
  项目名称: '项目名称',
  过期截至: '过期截至',
  链接: '链接',
  密码: '密码',
  不需要密码: '不需要密码',
  字段名: '字段名',
  参数字段名称: '参数字段名称',
  类型: '类型',
  Path参数个数: 'Path参数个数',
  Query参数个数: 'Query参数个数',
  Body参数个数: 'Body参数个数',
  Response参数个数: 'Response参数个数',
  参数名称: '参数名称',
  备注: '备注',
  参数值: '参数值',
  参数类型: '参数类型',
  是否删除当前参数: '是否删除当前参数',
  确定批量删除当前选中节点: '确定批量删除当前选中节点',
  过滤条件: '过滤条件',
  新增模板: '新增模板',
  '例如：默认返回值': '例如：默认返回值',
  '请求参数(Params)': '请求参数(Params)',
  '请求参数(Body)': '请选择参数类型',
  确认新增: '确认新增',
  修改模板: '修改模板',
  请选择参数类型: '请选择参数类型',
  创建者名称: '创建者名称',
  参数模板: '参数模板',
  请输入模板名称: '请输入模板名称',
  请选择模板类型: '请选择模板类型',
  新增变量: '新增变量',
  变量名称: '变量名称',
  请输入变量名称: '请输入变量名称',
  变量值: '变量值',
  请输入变量值: '请输入变量值',
  值类型: '值类型',
  变量列表: '变量列表',
  创建者: '创建者',
  '此操作将永久删除该域名, 是否继续?': '此操作将永久删除该域名, 是否继续?',
  '此操作将永久删除该变量, 是否继续?': '此操作将永久删除该变量, 是否继续?',
  删除成功: '删除成功',
  新增文档: '新增文档',
  文档名称: '文档名称',
  文件夹名称: '文件夹名称',
  关闭右侧: '关闭右侧',
  关闭左侧: '关闭左侧',
  关闭其他: '关闭其他',
  全部关闭: '全部关闭',
  全部: '全部',
  强制全部关闭: '强制全部关闭',
  新增项目: '新增项目',
  选择成员: '选择成员',
  请输入项目名称: '请输入项目名称',
  输入用户名或完整手机号查找用户: '输入用户名或完整手机号查找用户',
  用户名: '用户名',
  昵称: '昵称',
  '角色(权限)': '角色(权限)',
  只读: '只读',
  仅查看项目: '仅查看项目',
  读写: '读写',
  新增和编辑文档: '新增和编辑文档',
  管理员: '管理员',
  添加新成员: '添加新成员',
  请填写项目名称: '请填写项目名称',
  请勿重复添加: '请勿重复添加',
  '用户已存在、请勿重复添加': '用户已存在、请勿重复添加',
  修改项目: '修改项目',
  添加用户: '添加用户',
  '确认删除当前成员吗?': '确认删除当前成员吗?',
  团队至少保留一个管理员: '团队至少保留一个管理员',
  '确认离开当前团队吗?': '确认离开当前团队吗?',
  '确认改变当前管理员权限吗?': '确认改变当前管理员权限吗?',
  成员管理: '成员管理',
  新建项目: '新建项目',
  导入项目: '导入项目',
  收藏的项目: '收藏的项目',
  收藏: '收藏',
  取消收藏: '取消收藏',
  最新更新: '最新更新',
  接口数: '接口数',
  全部项目: '全部项目',
  团队管理: '团队管理',
  过期倒计时: '过期倒计时',
  确认密码: '确认密码',
  无效的项目id: '无效的项目id',
  '当前接口不存在，可能已经被删除!': '当前接口不存在，可能已经被删除!',
  关闭接口: '关闭接口',
  发送请求时候自动计算: '发送请求时候自动计算',
  消息的长度: '消息的长度',
  发送请求时候自动处理: '发送请求时候自动处理',
  用户代理软件信息: '用户代理软件信息',
  主机信息: '主机信息',
  客户端理解的编码方式: '客户端理解的编码方式',
  '当前的事务完成后，是否会关闭网络连接': '当前的事务完成后，是否会关闭网络连接',
  根据body类型自动处理: '根据body类型自动处理',
  返回参数名称: '返回参数名称',
  是否要保存对接口的修改: '是否要保存对接口的修改',
  '维护人员：': '维护人员：',
  '创建人员：': '创建人员：',
  '累计用时：': '累计用时：',
  '更新日期：': '更新日期：',
  '创建日期：': '创建日期：',
  '确定批量删除个节点?': '确定批量删除  个节点?',
  '确定删除节点?': '确定删除  节点?',
  未知请求类型: '未知请求类型',
  模板维护: '模板维护',
  新增前端路由: '新增前端路由',
  分组名称: '分组名称',
  '名称&地址': '名称&地址',
  新增路由: '新增路由',
  批量修改类型: '批量修改类型',
  路由名称: '路由名称',
  路由地址: '路由地址',
  编辑菜单: '编辑菜单',
  菜单名称: '菜单名称',
  菜单列表: '菜单列表',
  新增子菜单: '新增子菜单',
  支持鼠标右键新增和编辑菜单: '支持鼠标右键新增和编辑菜单',
  菜单可以进行拖拽排序: '菜单可以进行拖拽排序',
  参数值不能为null: '参数值不能为null',
  全选: '全选',
  修改角色: '修改角色',
  角色名称: '角色名称',
  前端路由: '前端路由',
  后端路由: '后端路由',
  前端菜单: '前端菜单',
  创建时间: '创建时间',
  新增角色: '新增角色',
  修改服务端路由: '修改服务端路由',
  请求方法: '请求方法',
  批量修改服务端路由类型: '批量修改服务端路由类型',
  '新增用户': '新增用户',
  登录名称: '登录名称',
  手机号: '手机号',
  角色选择: '角色选择',
  修改: '修改',
  基础信息: '基础信息',
  下载模板: '下载模板',
  导入用户: '导入用户',
  上次登录: '上次登录',
  登录次数: '登录次数',
  角色信息: '角色信息',
  状态: '状态',
  启用: '启用',
  禁用: '禁用',
  确实要该用户吗: '确实要该用户吗',
  角色维护: '角色维护',
  菜单维护: '菜单维护',
  '后端路由(接口)': '后端路由(接口)',
  常用: '常用',
  '日期/时间': '日期/时间',
  图片: '图片',
  中文文本: '中文文本',
  英文文本: '英文文本',
  地区相关: '地区相关',
  颜色: '颜色',
  中文名称: '中文名称',
  中文单词: '中文单词',
  中文句子: '中文句子',
  中文段落: '中文段落',
  中文标题: '中文标题',
  英文名称: '英文名称',
  英文句子: '英文句子',
  英文单词: '英文单词',
  英文标题: '英文标题',
  布尔值: '布尔值',
  '自然数(0,1,2,3,4)': '自然数(0,1,2,3,4)',
  '自然数(大于100)': '自然数(大于100)',
  '自然数(大于100小于200)': '自然数(大于100小于200)',
  '整数(-22,1,23)': '整数(-22,1,23)',
  '整数(大于100)': '整数(大于100)',
  '整数(大于100小于200)': '整数(大于100小于200)',
  浮点数: '浮点数',
  字符串: '字符串',
  英文段落: '英文段落',
  数字: '数字',
  '字符串(长度为5)': '字符串(长度为5)',
  '时间戳(精确到毫秒13位)': '时间戳(精确到毫秒13位)',
  日期时间: '日期时间',
  '日期(年月日)': '日期(年月日)',
  '时间(时分秒)': '时间(时分秒)',
  当前日期时间: '当前日期时间',
  '颜色(#ff6600)': '颜色(#ff6600)',
  '颜色(rgb(122,122,122))': '颜色(rgb(122,122,122))',
  '颜色rgb(122,122,122, 0.3)': '颜色rgb(122,122,122, 0.3)',
  '颜色hsl(222, 11, 31)': '颜色hsl(222, 11, 31)',
  '图片(150x100)': '图片(150x100)',
  base64图片数据: 'base64图片数据',
  base64图片数据100x100: 'base64图片数据100x100',
  省: '省',
  市: '市',
  区: '区',
  此项不允许删除: '此项不允许删除',
  删除当前行: '删除当前行',
  传输数据类型为formData才能使用file类型: '传输数据类型为formData才能使用file类型',
  对象和数组不必填写参数值: '对象和数组不必填写参数值',
  请选择: '请选择',
  选择文件: '选择文件',
  必有: '必有',
  参数描述与备注: '参数描述与备注',
  '参数不允许嵌套，例如：当请求方式为get时，请求参数只能为扁平数据': '参数不允许嵌套，例如：当请求方式为get时，请请求参数只能为扁平数据',
  添加一条嵌套数据: '添加一条嵌套数据',
  根元素: '根元素',
  父元素为数组不必填写参数名称: '父元素为数组不必填写参数名称',
  输入参数名称自动换行: '输入参数名称自动换行',
  校验: '校验',
  '参数类型不允许改变，eg：当请求方式为get时，请求参数类型只能为string': '参数类型不允许改变，eg：当请求方式为get时，请请求参数类型只能为string',
  对象类型不必填写: '对象类型不必填写',
  至少保留一条数据: '至少保留一条数据',
  可选: '可选',
  请输入标题: '请输入标题',
  path参数: 'path参数',
  加载中: '加载中',
  请输入: '请输入',
  双击还原: '双击还原',
  获取验证码: '获取验证码',
  重新发送: '重新发送',
  重新获取: '重新获取',
  序号: '序号',
  在左侧进行数据选择后方可删除数据: '在左侧进行数据选择后方可删除数据',
  '此操作将删除条记录, 是否继续?': '此操作将删除  条记录, 是否继续?',
  用户: '用户',
  请求url不能为空: '请求url不能为空',
  参数位置: '参数位置',
  清除所有缓存: '清除所有缓存',
  变量类型: '变量类型',
  请求信息: '请求信息',
  生成代码: '生成代码',
  全局变量: '全局变量',
  '开始时间，可接受两个可选参数startTime(\'2022-xx-xx\', \'YYYY-MM-DD\')': '开始时间，可接受两个可选参数startTime(\'2022-xx-xx\', \'YYYY-MM-DD\')',
  '结束时间(结束时间晚于开始时间)': '结束时间(结束时间晚于开始时间)',
  点击工具栏按钮新建接口或者鼠标右键新增: '点击工具栏按钮新建接口或者鼠标右键新增',
  '{0} fileValue {1}': '{0} fileValue {1}',
  '输入变量；eg: ': '输入变量；eg: ',
  新建接口: '新建接口',
  设置公共请求头: '设置公共请求头',
  前缀值: '前缀值',
  什么是接口前缀: '什么是接口前缀',
  '域名、接口前缀': '域名、接口前缀',
  '时间戳(精确到秒10位)': '时间戳(精确到秒10位)',
  接口编排: '接口编排',
  返回首页: '返回首页',
  退出: '退出',
  '文件名称：': '文件名称：',
  'mime类型：': 'mime类型：',
  '文件路径：': '文件路径：',
  '注意：': '注意：',
  '若file类型变量大于10kb则会自动转换为本地附件地址，这可能导致隐私泄露，请仅添加授信成员': '若file类型变量大于10kb则会自动转换为本地附件地址，这可能导致隐私泄露，请仅添加授信成员',
  密码设置: '密码设置',
  密码可不填写: '密码可不填写',
  请输入链接名称: '请输入链接名称',
  '请输入链接名称 eg:xxx团队': '请输入链接名称 eg:xxx团队',
  过期时间: '过期时间',
  '不填默认一个月后过期，最大日期为一年': '不填默认一个月后过期，最大日期为一年',
  '1天后': '1天后',
  '1周后': '1周后',
  '1个月后': '1个月后',
  '1个季度后': '1个季度后',
  不过期: '不过期',
  选择分享: '选择分享',
  '开启后可以自由选择需要分享的文档': '开启后可以自由选择需要分享的文档',
  确认修改: '确认修改',
  '请至少选择一个文档分享': '请至少选择一个文档分享',
  天后: '天后',
  文档分享: '文档分享',
  分享链接: '分享链接',
  验证分享链接: '验证分享链接',
  正在验证分享链接: '正在验证分享链接',
  永久有效: '永久有效',
  文档内容: '文档内容',
  这里是分享的API文档内容: '这里是分享的API文档内容',
  密码验证成功: '密码验证成功',
  密码错误: '密码错误',
  密码验证失败: '密码验证失败',
  请输入访问密码: '请输入访问密码',
  分享链接无效缺少分享ID: '分享链接无效，缺少分享ID',
  获取分享信息失败请检查链接是否正确: '获取分享信息失败，请检查链接是否正确',
  已过期: '已过期',
  天: '天',
  小时: '小时',
  分: '分',
  秒: '秒',
  认证中: '认证中...',
  'Query 参数': 'Query 参数',
  'shareId为空，无法获取banner数据': 'shareId为空，无法获取banner数据',
  '获取分享banner数据失败': '获取分享banner数据失败',
  '获取分享数据失败，请检查分享链接是否有效': '获取分享数据失败，请检查分享链接是否有效',
  更新于: '更新于',
  '暂无Query参数': '暂无Query参数',
  参数名: '参数名',
  必填: '必填',
  描述: '描述',
  是: '是',
  否: '否',
  暂无请求头: '暂无请求头',
  暂无请求体参数: '暂无请求体参数',
  响应: '响应',
  暂无响应数据: '暂无响应数据',
  暂无标签页: '暂无标签页',
  分钟: '分钟',
  api_import_title: 'API文档导入',
  api_import_desc: '导入并校验你的Swagger或OpenAPI 3.1规范',
  // 个人中心相关翻译
  个人设置: '个人设置',
  缓存管理: '缓存管理',
  密码修改: '密码修改',
  邮箱: '邮箱',
  注册时间: '注册时间',
  最后登录: '最后登录',
  更换头像: '更换头像',
  编辑信息: '编辑信息',
  主题设置: '主题设置',
  主题模式: '主题模式',
  浅色主题: '浅色主题',
  深色主题: '深色主题',
  跟随系统: '跟随系统',
  主题色: '主题色',
  语言设置: '语言设置',
  界面语言: '界面语言',
  界面设置: '界面设置',
  紧凑模式: '紧凑模式',
  显示侧边栏: '显示侧边栏',
  其他设置: '其他设置',
  自动保存: '自动保存',
  启动时恢复标签页: '启动时恢复标签页',
  保存所有设置: '保存所有设置',
  恢复默认: '恢复默认',
  缓存概览: '缓存概览',
  总缓存大小: '总缓存大小',
  缓存项目: '缓存项目',
  缓存分类管理: '缓存分类管理',
  项目数: '项目数',
  查看详情: '查看详情',
  缓存详情: '缓存详情',
  缓存键: '缓存键',
  原密码: '原密码',
  新密码: '新密码',
  请输入原密码: '请输入原密码',
  请输入新密码: '请输入新密码',
  请再次输入新密码: '请再次输入新密码',
  密码强度: '密码强度',
  密码要求: '密码要求',
  至少8个字符: '至少8个字符',
  包含大写字母: '包含大写字母',
  包含小写字母: '包含小写字母',
  包含数字: '包含数字',
  包含特殊字符: '包含特殊字符',
  确认修改密码: '确认修改密码',
  重置: '重置',
  安全提示: '安全提示',
  '建议使用包含大小写字母、数字和特殊字符的强密码': '建议使用包含大小写字母、数字和特殊字符的强密码',
  '不要使用与其他账户相同的密码': '不要使用与其他账户相同的密码',
  '定期更换密码以确保账户安全': '定期更换密码以确保账户安全',
  详情: '详情',
  键名: '键名',
  无法解析数据: '无法解析数据',
  // WebSocket相关翻译
  自动发送间隔: '自动发送间隔',
  自动发送消息: '自动发送消息',
  自动发送: '自动发送',
  发送并清空: '发送并清空',
  自动发送内容: '自动发送内容',
  启用自动发送: '启用自动发送',
  自动发送间隔时间: '自动发送间隔时间',
  毫秒: '毫秒',
  自定义自动发送内容: '自定义自动发送内容',
  自动发送成功: '自动发送成功',
  自动发送失败: '自动发送失败',
  请输入自动发送内容: '请输入自动发送内容',
  自动发送间隔不能为空: '自动发送间隔不能为空',
  自动发送间隔必须大于0: '自动发送间隔必须大于0',
  自动发送已启用: '自动发送已启用',
  自动发送已停止: '自动发送已停止',
  发送间隔: '发送间隔',
  消息模板: '消息模板',
  选择模板: '选择模板',
  暂无模板数据: '暂无模板数据',
  创建模板: '创建模板',
  创建消息模板: '创建消息模板',
  消息模板名称: '模板名称',
  消息数据类型: '数据类型',
  消息数据值: '数据值',
  请输入消息模板名称: '请输入模板名称',
  请输入消息数据值: '请输入数据值',
  消息模板名称不能为空: '模板名称不能为空',
  确定要删除此消息模板吗: '确定要删除此模板吗？',
  消息模板删除成功: '模板删除成功',
  消息模板创建成功: '模板创建成功',
  '模板名称长度在 1 到 50 个字符': '模板名称长度在 1 到 50 个字符',
  请选择数据类型: '请选择数据类型',
  模板名称已存在: '模板名称已存在',
  模板创建失败: '模板创建失败',
  模板删除失败: '模板删除失败',

  // 新增的未使用i18n的文本
  '本地': '本地',
  中文简体: '中文简体',
  中文繁體: '中文繁體',
  主页面: '主页面',
  最小化: '最小化',
  最大化: '最大化',
  取消最大化: '取消最大化',
  刷新主应用: '刷新主应用',
  切换变量选择模式支持变量或者直接选择文件: '切换变量选择模式，支持变量或者直接选择文件',
  变量模式: '变量模式',
  文件模式: '文件模式',
  不允许新增数据: '不允许新增数据',
  不允许删除数据: '不允许删除数据',

  // WebSocket撤销重做相关
  撤销上一步操作: '撤销上一步操作',
  重做下一步操作: '重做下一步操作',
  没有可撤销的操作: '没有可撤销的操作',
  没有可重做的操作: '没有可重做的操作',
  撤销成功: '撤销成功',
  重做成功: '重做成功',
  撤销失败: '撤销失败',
  重做失败: '重做失败',
  历史记录: '历史记录',
  清空历史记录: '清空历史记录',
  配置更新: '配置更新',
  请求头更新: '请求头更新',
  查询参数更新: '查询参数更新',
  发送消息更新: '发送消息更新',
  连接配置更新: '连接配置更新',
  初始化状态: '初始化状态',
  重置状态: '重置状态',
  该请求头无法修改也无法取消发送: '该请求头无法修改，也无法取消发送',
  执行中: '执行中...',
  此操作将清空所有本地缓存是否继续: '此操作将清空所有本地缓存, 是否继续?',
  已被删除: '已被删除',
  连接中: '连接中',
  已连接: '已连接',
  连接失败: '连接失败',
  已断开: '已断开',
  发送: '发送',
  连接: '连接',
  断开连接: '断开连接',
  消息历史: '消息历史',
  清空历史: '清空历史',
  发送消息: '发送消息',
  请输入消息内容: '请输入消息内容',
  连接地址: '连接地址',
  请输入WebSocket连接地址: '请输入WebSocket连接地址',
  连接参数: '连接参数',
  请求头参数: '请求头参数',
  WebSocket连接测试: 'WebSocket连接测试',
  点击连接按钮建立WebSocket连接: '点击连接按钮建立WebSocket连接',
  项目特色功能视频演示: '项目特色功能视频演示',
  下载: '下载',
  个人基本信息: '个人基本信息',
  用户头像: '用户头像',
  返回上级: '返回上级',
  修改密码: '修改密码',
  支持正则表达式: '支持正则表达式，如: /pattern/flags 或 pattern',
  输入关键词筛选: '输入关键词筛选消息内容...',
  切换正则表达式模式: '切换正则表达式模式',
  切换原始数据视图: '切换原始数据视图',
  下载SSE数据: '下载SSE数据',
  找到: '找到',
  条匹配结果: '条匹配结果',
  未找到匹配结果: '未找到匹配结果',
  下载WebSocket数据: '下载WebSocket数据',
  接收: '接收',
  开始连接: '开始连接',
  重连中: '重连中',
  正则表达式错误: '正则表达式错误',
  未知错误: '未知错误',
  下载失败: '下载失败',
  重试第: '重试第',
  次URL: '次，URL:',
  消息详情: '消息详情',
  错误信息: '错误信息',
  内容类型: '内容类型',
  重连次数: '重连次数',
  下次重试: '下次重试',
  断开原因: '断开原因',
  手动断开: '手动断开',
  自动断开: '自动断开',
  全部消息: '全部消息',

  // 新增缺失的翻译条目 - 第一批
  '1. 公共请求头针对目录内所有接口生效': '1. 公共请求头针对目录内所有接口生效',
  '12小时后': '12小时后',
  '2. 针对嵌套目录，子目录优先级高于父目录': '2. 针对嵌套目录，子目录优先级高于父目录',
  '24小时后': '24小时后',
  '3. 接口本身请求头优先级高于公共请求头': '3. 接口本身请求头优先级高于公共请求头',
  '7天后': '7天后',
  'Cookies': 'Cookies',
  'Cookies 管理': 'Cookies 管理',
  'Cookie管理': 'Cookie管理',
  'cookie值': 'cookie值',
  'HTML': 'HTML',
  'HttpOnly': 'HttpOnly',
  'HTTP返回状态码：': 'HTTP返回状态码：',
  'JSON': 'JSON',
  'Lax': 'Lax',
  'None': 'None',
  'SameSite': 'SameSite',
  'Secure': 'Secure',
  'Strict': 'Strict',
  'XML': 'XML',
  按名称搜索: '按名称搜索',
  按域名筛选: '按域名筛选',
  保存备注: '保存备注',
  保存接口后更新: '保存接口后更新',
  '保存修改【添加、更新成员不需要保存，直接生效】': '保存修改【添加、更新成员不需要保存，直接生效】',
  本次接口返回的cookie值: '本次接口返回的cookie值',
  '编辑 Cookie': '编辑 Cookie',
  '参数不允许嵌套，例如：当请求方式为get时，请请求参数只能为扁平数据': '参数不允许嵌套，例如：当请求方式为get时，请请求参数只能为扁平数据',
  测试脚本: '测试脚本',
  插入变量: '插入变量',
  插入函数: '插入函数',
  查看: '查看',
  超过最大预览限制: '超过最大预览限制',
  超长部分被截断: '超长部分被截断',
  创建链接: '创建链接',
  创建团队: '创建团队',
  '此操作将清空所有本地缓存, 是否继续?': '此操作将清空所有本地缓存, 是否继续?',
  次: '次',
  从URL中导入: '从URL中导入',
  从粘贴内容中导入: '从粘贴内容中导入',
  代码预览: '代码预览',
  导出失败: '导出失败',
  等待发送请求: '等待发送请求',
  等待数据返回: '等待数据返回',
  点击展开: '点击展开',
  断开连接失败: '断开连接失败',
  断开连接异常: '断开连接异常',
  '二进制(Base64)': '二进制(Base64)',
  '二进制(Hex)': '二进制(Hex)',
  二进制数据: '二进制数据',
  发起连接: '发起连接',
  返回结果: '返回结果',
  复制url: '复制url',
  '该请求头无法修改，也无法取消发送': '该请求头无法修改，也无法取消发送',
  高级搜索: '高级搜索',
  格式化: '格式化',
  公共请求头: '公共请求头',
  关闭中: '关闭中',
  '管理项目的在线分享链接，方便外部人员访问': '管理项目的在线分享链接，方便外部人员访问',
  还原: '还原',
  加载模板: '加载模板',
  键: '键',
  接口类型: '接口类型',
  接口名称: '接口名称',
  解析错误: '解析错误',
  仅支持js文件上传: '仅支持js文件上传',
  来源: '来源',
  连接异常: '连接异常',
  '路径必须以 / 开头，否则无效': '路径必须以 / 开头，否则无效',
  模板选择: '模板选择',
  批量删除成功: '批量删除成功',
  批量删除确认: '批量删除确认',
  批量修改前端路由类型: '批量修改前端路由类型',
  '切换变量选择模式，支持变量或者直接选择文件': '切换变量选择模式，支持变量或者直接选择文件',
  切换列表展示: '切换列表展示',
  清空备注: '清空备注',
  清空脚本: '清空脚本',
  请求body: '请求body',
  请求URL: '请求URL',
  请输入备注: '请输入备注',
  请输入接口名称: '请输入接口名称',
  请输入名称: '请输入名称',
  请输入任意数字: '请输入任意数字',
  请输入任意字符: '请输入任意字符',
  请输入图形验证码: '请输入图形验证码',
  请输入团队名称: '请输入团队名称',
  请输入值: '请输入值',
  请填写团队名称: '请填写团队名称',
  请选择过期时间: '请选择过期时间',
  '权限修改和成员增加不需要保存，修改后立即生效': '权限修改和成员增加不需要保存，修改后立即生效',
  权限选择: '权限选择',
  全部返回头信息: '全部返回头信息',
  全部团队: '全部团队',
  全局: '全局',
  全局公共头: '全局公共头',
  '确定要删除此项目吗?': '确定要删除此项目吗?',
  '确定要删除该团队吗？': '确定要删除该团队吗？',
  '确定要删除选中的 ${selectedCookies.value.length} 个 Cookie 吗？': '确定要删除选中的 ${selectedCookies.value.length} 个 Cookie 吗？',
  '确定要删除这个 Cookie 吗？': '确定要删除这个 Cookie 吗？',
  '确定要移除该用户吗？': '确定要移除该用户吗？',
  确认要退出当前团队吗: '确认要退出当前团队吗',
  '确实要${tipLabel}该用户吗': '确实要${tipLabel}该用户吗',
  '若file类型变量大于10kb则会自动转换为本地文件地址\\n若协作者无此路径附件则会导致无法获取附件变量\\n这也可能导致隐私泄露，请仅添加授信成员': '若file类型变量大于10kb则会自动转换为本地文件地址\\n若协作者无此路径附件则会导致无法获取附件变量\\n这也可能导致隐私泄露，请仅添加授信成员',
  '若不选择，则会挂载在根节点': '若不选择，则会挂载在根节点',
  删除当前项: '删除当前项',
  删除确认: '删除确认',
  上传本地文件: '上传本地文件',
  实际保存值: '实际保存值',
  是否发送: '是否发送',
  '输入【用户名】| 【完整手机号】 | 【组名称】': '输入【用户名】| 【完整手机号】 | 【组名称】',
  输入参数名称: '输入参数名称',
  '输入接口url eg: 接口url': '输入接口url eg: 接口url',
  数据大小为: '数据大小为',
  搜索团队: '搜索团队',
  所属团队: '所属团队',
  所有域名生效: '所有域名生效',
  添加新用户: '添加新用户',
  图形验证码: '图形验证码',
  团队成员: '团队成员',
  团队列表: '团队列表',
  团队描述: '团队描述',
  '团队描述/备注': '团队描述/备注',
  团队名称: '团队名称',
  团队信息: '团队信息',
  团队邀请限制: '团队邀请限制',
  万能域名: '万能域名',
  未命名的接口: '未命名的接口',
  未能读取文件: '未能读取文件',
  未设置: '未设置',
  '未填写则代表所有请求都会携带这个cookie': '未填写则代表所有请求都会携带这个cookie',
  文本: '文本',
  文件大小不超过20M: '文件大小不超过20M',
  无效时间: '无效时间',
  下载到本地预览: '下载到本地预览',
  下载完整数据: '下载完整数据',
  消息发送失败: '消息发送失败',
  消息发送异常: '消息发送异常',
  '新增 Cookie': '新增 Cookie',
  新增菜单: '新增菜单',
  新增成功: '新增成功',
  新增服务端路由: '新增服务端路由',
  修改变量: '修改变量',
  修改前端路由: '修改前端路由',
  修改数据后可以保存: '修改数据后可以保存',
  选择成员或组: '选择成员或组',
  选择过期时间: '选择过期时间',
  选择需要挂载的节点: '选择需要挂载的节点',
  '延迟返回(毫秒)：': '延迟返回(毫秒)：',
  '已过期，发送请求不会生效，刷新页面后消失': '已过期，发送请求不会生效，刷新页面后消失',
  '已忽略，非本域名': '已忽略，非本域名',
  异常: '异常',
  '用户已经存在，请勿重复添加': '用户已经存在，请勿重复添加',
  由: '由',
  域名: '域名',
  在此处输入备注信息: '在此处输入备注信息',
  '暂无 Query 参数': '暂无 Query 参数',
  暂无文档: '暂无文档',
  展开显示返回头: '展开显示返回头',
  '支持openapi3.0规范文档、postman文档、apiflow格式文档': '支持openapi3.0规范文档、postman文档、apiflow格式文档',
  '执行中...': '执行中...',
  重定向: '重定向',
  重定向信息: '重定向信息',
  重新连接: '重新连接',
  '状态：': '状态：',
  自定义返回头: '自定义返回头',
  自动发送异常: '自动发送异常',

  // 新增缺失的翻译条目 - TypeScript文件中的key
  '<WebSocket协议版本，固定为13，值不可覆盖>': '<WebSocket协议版本，固定为13，值不可覆盖>',
  '<保持连接升级，WebSocket必需，值可覆盖>': '<保持连接升级，WebSocket必需，值可覆盖>',
  '<发送时候自动计算>': '<发送时候自动计算>',
  '<客户端标识，可选>': '<客户端标识，可选>',
  '<客户端自动生成随机Key>': '<客户端自动生成随机Key>',
  '<扩展功能协商，可选>': '<扩展功能协商，可选>',
  '<升级协议，WebSocket必需，值可覆盖>': '<升级协议，WebSocket必需，值可覆盖>',
  '<握手校验Key，客户端每次随机生成，值不可覆盖>': '<握手校验Key，客户端每次随机生成，值不可覆盖>',
  '<源验证，用于CORS安全检查，可选>': '<源验证，用于CORS安全检查，可选>',
  '<主机信息，WebSocket连接必需，值可覆盖>': '<主机信息，WebSocket连接必需，值可覆盖>',
  '<子协议协商，可选>': '<子协议协商，可选>',
  '<自动生成>': '<自动生成>',
  'Ctrl+R可以刷新页面': 'Ctrl+R可以刷新页面',
  'Ctrl+鼠标左键可以对banner进行批量操作': 'Ctrl+鼠标左键可以对banner进行批量操作',
  登录已过期: '登录已过期',
  分享链接不存在: '分享链接不存在',
  分享链接无需密码: '分享链接无需密码',
  分享链接已过期: '分享链接已过期',
  '接口调用超时，请稍后重试': '接口调用超时，请稍后重试',
  请求已取消: '请求已取消',
  跳转登录: '跳转登录',
  '未知的请求body类型': '未知的请求body类型',
  '系统开小差了!': '系统开小差了!',
  暂无权限: '暂无权限',
  刚刚: '刚刚',
  '{count}分钟前': '{count}分钟前',
  '{count}小时前': '{count}小时前',
  '{count}天前': '{count}天前',
  '{hours}小时{minutes}分钟前': '{hours}小时{minutes}分钟前',
}
